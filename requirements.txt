# SecVizLM 多模态安全分析系统依赖包

# Flask 核心框架
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.7

# API 文档
flasgger==0.9.7.1

# 多模态大语言模型
torch>=2.0.0
transformers>=4.35.0
accelerate>=0.24.0
peft>=0.6.0
bitsandbytes>=0.41.0

# 计算机视觉
opencv-python==4.8.1.78
Pillow>=10.0.0
numpy>=1.24.0

# OCR 文字识别
paddlepaddle>=2.5.0
paddleocr>=2.7.0

# 目标检测
ultralytics>=8.0.0

# 图像处理增强
scikit-image>=0.21.0
matplotlib>=3.7.0

# 数据处理
pandas>=2.0.0
requests>=2.31.0

# 安全和验证
cryptography>=41.0.0
python-magic>=0.4.27

# 日志和配置
python-dotenv>=1.0.0
pyyaml>=6.0

# 开发和测试工具
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0

# 性能监控
psutil>=5.9.0
memory-profiler>=0.61.0

# 可选：如果需要使用 OpenAI API 作为备用
# openai>=1.0.0

# 可选：如果需要数据库支持
# sqlalchemy>=2.0.0
# sqlite3  # Python 内置

# 可选：如果需要 Redis 缓存
# redis>=5.0.0

# 可选：如果需要消息队列
# celery>=5.3.0

# 注意事项：
# 1. torch 版本需要根据 CUDA 版本调整
# 2. paddlepaddle 可能需要根据系统选择 CPU 或 GPU 版本
# 3. 某些包可能需要额外的系统依赖
# 4. 建议使用虚拟环境安装

# 安装命令示例：
# pip install -r requirements.txt

# 如果使用 CUDA GPU：
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 如果使用 PaddlePaddle GPU 版本：
# pip install paddlepaddle-gpu
