"""
SecVizLM 多模态处理引擎
集成 Qwen-VL-Chat、PaddleOCR、YOLOv8 的多模态分析核心
"""

import cv2
import numpy as np
import torch
import logging
from PIL import Image
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import json
import base64
from io import BytesIO

# 导入相关库（需要在requirements.txt中添加）
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from peft import PeftModel, LoraConfig, get_peft_model
    import paddleocr
    from ultralytics import YOLO
except ImportError as e:
    logging.warning(f"某些依赖库未安装: {e}")

from config import MODEL_CONFIG, CV_CONFIG
from security_utils import ThreatAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiModalEngine:
    """多模态分析引擎"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"使用设备: {self.device}")
        
        # 初始化组件
        self.qwen_model = None
        self.qwen_tokenizer = None
        self.ocr_engine = None
        self.yolo_model = None
        self.threat_analyzer = ThreatAnalyzer()
        
        # 延迟加载模型
        self._models_loaded = False
    
    def _load_models(self):
        """延迟加载所有模型"""
        if self._models_loaded:
            return
        
        try:
            logger.info("开始加载多模态模型...")
            
            # 加载 Qwen-VL-Chat
            self._load_qwen_model()
            
            # 加载 OCR 引擎
            self._load_ocr_engine()
            
            # 加载 YOLO 模型
            self._load_yolo_model()
            
            self._models_loaded = True
            logger.info("所有模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _load_qwen_model(self):
        """加载 Qwen-VL-Chat 模型"""
        try:
            model_name = MODEL_CONFIG["qwen_vl_model"]
            logger.info(f"加载 Qwen-VL 模型: {model_name}")
            
            # 加载分词器
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True
            )
            
            # 加载基础模型
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.float16 if MODEL_CONFIG["qwen_vl_precision"] == "fp16" else torch.float32
            )
            
            # 如果需要，可以在这里加载 LoRA 适配器
            # self._load_lora_adapter()
            
            logger.info("Qwen-VL 模型加载成功")
            
        except Exception as e:
            logger.error(f"Qwen-VL 模型加载失败: {e}")
            # 使用模拟模型进行开发测试
            self.qwen_model = None
            self.qwen_tokenizer = None
    
    def _load_ocr_engine(self):
        """加载 PaddleOCR 引擎"""
        try:
            logger.info("初始化 PaddleOCR 引擎")
            self.ocr_engine = paddleocr.PaddleOCR(**CV_CONFIG["ocr_config"])
            logger.info("PaddleOCR 引擎初始化成功")
        except Exception as e:
            logger.error(f"PaddleOCR 初始化失败: {e}")
            self.ocr_engine = None
    
    def _load_yolo_model(self):
        """加载 YOLOv8 模型"""
        try:
            model_path = CV_CONFIG["yolo_model"]
            logger.info(f"加载 YOLO 模型: {model_path}")
            self.yolo_model = YOLO(model_path)
            logger.info("YOLO 模型加载成功")
        except Exception as e:
            logger.error(f"YOLO 模型加载失败: {e}")
            self.yolo_model = None
    
    def process_multimodal_input(self, 
                                text_input: str = None, 
                                image_path: str = None) -> Dict[str, Any]:
        """处理多模态输入"""
        
        # 确保模型已加载
        self._load_models()
        
        result = {
            "text_analysis": {},
            "image_analysis": {},
            "multimodal_fusion": {},
            "security_insights": {}
        }
        
        # 处理文本输入
        if text_input:
            result["text_analysis"] = self._analyze_text(text_input)
        
        # 处理图像输入
        if image_path:
            result["image_analysis"] = self._analyze_image(image_path)
        
        # 多模态融合分析
        if text_input and image_path:
            result["multimodal_fusion"] = self._multimodal_fusion(
                text_input, image_path, result["image_analysis"]
            )
        
        # 生成安全洞察
        result["security_insights"] = self._generate_security_insights(result)
        
        return result
    
    def _analyze_text(self, text: str) -> Dict[str, Any]:
        """分析文本内容"""
        logger.info("开始文本分析")
        
        # 使用威胁分析器分析日志
        threat_analysis = self.threat_analyzer.analyze_log_content(text)
        
        # 提取关键信息
        key_info = self._extract_key_information(text)
        
        return {
            "threat_analysis": threat_analysis,
            "key_information": key_info,
            "text_length": len(text),
            "processed_at": "2025-01-09T10:00:00"
        }
    
    def _analyze_image(self, image_path: str) -> Dict[str, Any]:
        """分析图像内容"""
        logger.info(f"开始图像分析: {image_path}")
        
        result = {
            "ocr_results": {},
            "object_detection": {},
            "image_metadata": {}
        }
        
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法加载图像: {image_path}")
            
            # 图像预处理
            processed_image = self._preprocess_image(image)
            
            # OCR 文字识别
            if self.ocr_engine:
                result["ocr_results"] = self._perform_ocr(processed_image)
            
            # 目标检测
            if self.yolo_model:
                result["object_detection"] = self._perform_object_detection(processed_image)
            
            # 图像元数据
            result["image_metadata"] = self._extract_image_metadata(image, image_path)
            
        except Exception as e:
            logger.error(f"图像分析失败: {e}")
            result["error"] = str(e)
        
        return result
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        # 调整大小
        max_size = CV_CONFIG["image_preprocessing"]["max_size"]
        h, w = image.shape[:2]
        
        if max(h, w) > max(max_size):
            scale = max(max_size) / max(h, w)
            new_w, new_h = int(w * scale), int(h * scale)
            image = cv2.resize(image, (new_w, new_h))
        
        # 增强对比度
        if CV_CONFIG["image_preprocessing"]["enhance_contrast"]:
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            image = cv2.merge([l, a, b])
            image = cv2.cvtColor(image, cv2.COLOR_LAB2BGR)
        
        return image
    
    def _perform_ocr(self, image: np.ndarray) -> Dict[str, Any]:
        """执行 OCR 文字识别"""
        try:
            results = self.ocr_engine.ocr(image, cls=True)
            
            extracted_text = []
            text_boxes = []
            
            for line in results[0] if results[0] else []:
                if line:
                    box, (text, confidence) = line
                    if confidence > 0.5:  # 置信度阈值
                        extracted_text.append(text)
                        text_boxes.append({
                            "text": text,
                            "confidence": confidence,
                            "bbox": box
                        })
            
            return {
                "extracted_text": " ".join(extracted_text),
                "text_boxes": text_boxes,
                "total_text_regions": len(text_boxes)
            }
            
        except Exception as e:
            logger.error(f"OCR 处理失败: {e}")
            return {"error": str(e)}
    
    def _perform_object_detection(self, image: np.ndarray) -> Dict[str, Any]:
        """执行目标检测"""
        try:
            results = self.yolo_model(image, conf=CV_CONFIG["yolo_confidence"])
            
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        detections.append({
                            "class": result.names[int(box.cls)],
                            "confidence": float(box.conf),
                            "bbox": box.xyxy[0].tolist()
                        })
            
            return {
                "detections": detections,
                "total_objects": len(detections)
            }
            
        except Exception as e:
            logger.error(f"目标检测失败: {e}")
            return {"error": str(e)}
    
    def _extract_image_metadata(self, image: np.ndarray, image_path: str) -> Dict[str, Any]:
        """提取图像元数据"""
        return {
            "dimensions": f"{image.shape[1]}x{image.shape[0]}",
            "channels": image.shape[2] if len(image.shape) > 2 else 1,
            "file_size": Path(image_path).stat().st_size,
            "format": Path(image_path).suffix.lower()
        }
    
    def _multimodal_fusion(self, text: str, image_path: str, image_analysis: Dict) -> Dict[str, Any]:
        """多模态融合分析"""
        logger.info("执行多模态融合分析")
        
        # 提取图像中的文本
        image_text = image_analysis.get("ocr_results", {}).get("extracted_text", "")
        
        # 合并文本信息
        combined_text = f"{text}\n图像文本: {image_text}"
        
        # 使用 Qwen-VL 进行多模态推理
        multimodal_insights = self._qwen_multimodal_inference(combined_text, image_path)
        
        return {
            "combined_text_length": len(combined_text),
            "image_text_extracted": len(image_text) > 0,
            "multimodal_insights": multimodal_insights,
            "fusion_confidence": 0.8  # 简化的置信度
        }
    
    def _qwen_multimodal_inference(self, text: str, image_path: str) -> Dict[str, Any]:
        """使用 Qwen-VL 进行多模态推理"""
        
        if not self.qwen_model or not self.qwen_tokenizer:
            # 模拟推理结果用于开发测试
            return {
                "analysis": "基于文本和图像的综合分析显示可能存在安全威胁",
                "confidence": 0.75,
                "key_findings": ["异常访问模式", "可疑IP地址", "错误响应码"],
                "model_used": "simulated"
            }
        
        try:
            # 构建多模态输入
            prompt = f"""
            作为网络安全专家，请分析以下安全日志和相关截图：
            
            日志内容：
            {text}
            
            请提供：
            1. 威胁类型识别
            2. 攻击向量分析
            3. 影响评估
            4. 应对建议
            """
            
            # 这里应该是实际的 Qwen-VL 推理代码
            # 由于模型较大，这里提供框架代码
            
            return {
                "analysis": "多模态分析完成",
                "confidence": 0.85,
                "model_used": "qwen-vl-chat"
            }
            
        except Exception as e:
            logger.error(f"Qwen-VL 推理失败: {e}")
            return {"error": str(e)}
    
    def _extract_key_information(self, text: str) -> Dict[str, Any]:
        """提取关键信息"""
        # IP地址
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ips = list(set(re.findall(ip_pattern, text)))
        
        # HTTP状态码
        status_pattern = r'\b[1-5]\d{2}\b'
        status_codes = list(set(re.findall(status_pattern, text)))
        
        # URL路径
        url_pattern = r'["\s](\/[^\s"]*)'
        urls = list(set(re.findall(url_pattern, text)))
        
        return {
            "ip_addresses": ips[:10],
            "status_codes": status_codes[:10],
            "url_paths": urls[:10]
        }
    
    def _generate_security_insights(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成安全洞察"""
        insights = {
            "risk_level": "Medium",
            "key_indicators": [],
            "recommendations": [],
            "context": ""
        }
        
        # 从文本分析中提取威胁
        text_threats = analysis_result.get("text_analysis", {}).get("threat_analysis", {}).get("threats", [])
        
        # 从图像分析中提取信息
        image_text = analysis_result.get("image_analysis", {}).get("ocr_results", {}).get("extracted_text", "")
        
        if text_threats:
            insights["risk_level"] = "High"
            insights["key_indicators"].extend(text_threats)
        
        if image_text:
            insights["context"] = f"图像中发现文本信息: {image_text[:100]}..."
        
        return insights

# 工具函数
import re

def encode_image_to_base64(image_path: str) -> str:
    """将图像编码为 base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')
