"""
SecVizLM Flask 主应用
多模态安全事件分析报告生成系统 API 服务
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
from flasgger import Swagger, swag_from

from config import FLASK_CONFIG, DATA_DIRS
from security_utils import SecurityInputFilter, SecurityReportGenerator
from multimodal_engine import MultiModalEngine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 Flask 应用
app = Flask(__name__)
app.config.update(FLASK_CONFIG)

# 启用 CORS
CORS(app)

# 配置 Swagger
swagger_config = {
    "headers": [],
    "specs": [
        {
            "endpoint": 'apispec',
            "route": '/apispec.json',
            "rule_filter": lambda rule: True,
            "model_filter": lambda tag: True,
        }
    ],
    "static_url_path": "/flasgger_static",
    "swagger_ui": True,
    "specs_route": "/docs/"
}

swagger = Swagger(app, config=swagger_config)

# 初始化组件
security_filter = SecurityInputFilter()
report_generator = SecurityReportGenerator()
multimodal_engine = MultiModalEngine()

# 确保上传目录存在
os.makedirs(FLASK_CONFIG["upload_folder"], exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in FLASK_CONFIG["allowed_extensions"]

@app.errorhandler(RequestEntityTooLarge)
def handle_file_too_large(e):
    """处理文件过大错误"""
    return jsonify({
        "error": "文件过大",
        "message": f"文件大小不能超过 {FLASK_CONFIG['max_content_length'] // (1024*1024)}MB"
    }), 413

@app.errorhandler(Exception)
def handle_exception(e):
    """全局异常处理"""
    logger.error(f"未处理的异常: {e}")
    return jsonify({
        "error": "内部服务器错误",
        "message": str(e)
    }), 500

@app.route('/')
def index():
    """主页"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>SecVizLM - 多模态安全分析系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .feature { margin: 20px 0; padding: 15px; background: #ecf0f1; border-radius: 5px; }
            .api-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
            .api-link:hover { background: #2980b9; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🛡️ SecVizLM</h1>
            <h2>多模态安全事件分析报告生成系统</h2>
            
            <div class="feature">
                <h3>🔍 核心功能</h3>
                <ul>
                    <li>多模态输入：安全日志文本 + 告警截图</li>
                    <li>智能分析：基于 Qwen-VL-Chat 的威胁识别</li>
                    <li>结构化输出：OWASP 标准的安全报告</li>
                    <li>实时处理：OCR + 目标检测 + LLM 融合</li>
                </ul>
            </div>
            
            <div class="feature">
                <h3>🚀 API 接口</h3>
                <a href="/docs/" class="api-link">📖 API 文档</a>
                <a href="/health" class="api-link">💚 健康检查</a>
                <a href="/api/analyze" class="api-link">🔬 分析接口</a>
            </div>
            
            <div class="feature">
                <h3>🛠️ 技术栈</h3>
                <p><strong>多模态模型:</strong> Qwen-VL-Chat</p>
                <p><strong>计算机视觉:</strong> PaddleOCR + YOLOv8</p>
                <p><strong>后端框架:</strong> Flask + Swagger</p>
                <p><strong>安全特性:</strong> OWASP 威胁分析</p>
            </div>
        </div>
    </body>
    </html>
    """
    return render_template_string(html_template)

@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    ---
    tags:
      - System
    responses:
      200:
        description: 系统状态正常
        schema:
          type: object
          properties:
            status:
              type: string
              example: "healthy"
            timestamp:
              type: string
              example: "2025-01-09T10:00:00"
            version:
              type: string
              example: "1.0.0"
    """
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "components": {
            "multimodal_engine": "ready",
            "security_filter": "ready",
            "report_generator": "ready"
        }
    })

@app.route('/api/analyze', methods=['POST'])
def analyze_security_event():
    """
    多模态安全事件分析
    ---
    tags:
      - Analysis
    consumes:
      - multipart/form-data
    parameters:
      - name: log_text
        in: formData
        type: string
        required: false
        description: 安全日志文本内容
      - name: alert_image
        in: formData
        type: file
        required: false
        description: 告警截图文件
    responses:
      200:
        description: 分析成功
        schema:
          type: object
          properties:
            success:
              type: boolean
            report:
              type: object
              properties:
                threat_type:
                  type: string
                  example: "SQL注入攻击"
                affected_assets:
                  type: array
                  items:
                    type: string
                attack_timeline:
                  type: string
                critical_indicators:
                  type: array
                  items:
                    type: string
                recommended_actions:
                  type: array
                  items:
                    type: string
                severity_level:
                  type: string
                  example: "High"
                confidence_score:
                  type: number
                  example: 0.85
      400:
        description: 请求参数错误
      500:
        description: 服务器内部错误
    """
    try:
        # 获取文本输入
        log_text = request.form.get('log_text', '').strip()
        
        # 处理文件上传
        uploaded_file = request.files.get('alert_image')
        image_path = None
        
        if uploaded_file and uploaded_file.filename:
            if not allowed_file(uploaded_file.filename):
                return jsonify({
                    "success": False,
                    "error": "不支持的文件格式",
                    "allowed_formats": list(FLASK_CONFIG["allowed_extensions"])
                }), 400
            
            # 保存上传的文件
            filename = secure_filename(uploaded_file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            image_path = os.path.join(FLASK_CONFIG["upload_folder"], filename)
            uploaded_file.save(image_path)
            
            # 验证图像文件
            try:
                security_filter.validate_image_upload(Path(image_path))
            except ValueError as e:
                os.remove(image_path)  # 删除无效文件
                return jsonify({
                    "success": False,
                    "error": str(e)
                }), 400
        
        # 检查输入
        if not log_text and not image_path:
            return jsonify({
                "success": False,
                "error": "请提供日志文本或告警图像"
            }), 400
        
        # 清洗文本输入
        if log_text:
            try:
                log_text = security_filter.sanitize_text_input(log_text)
            except ValueError as e:
                return jsonify({
                    "success": False,
                    "error": str(e)
                }), 400
        
        # 执行多模态分析
        logger.info(f"开始分析 - 文本长度: {len(log_text) if log_text else 0}, 图像: {bool(image_path)}")
        
        analysis_result = multimodal_engine.process_multimodal_input(
            text_input=log_text,
            image_path=image_path
        )
        
        # 生成安全报告
        threat_analysis = analysis_result.get("text_analysis", {}).get("threat_analysis", {})
        multimodal_insights = analysis_result.get("multimodal_fusion", {})
        
        security_report = report_generator.generate_report(
            threat_analysis=threat_analysis,
            multimodal_insights=multimodal_insights
        )
        
        # 清理临时文件
        if image_path and os.path.exists(image_path):
            try:
                os.remove(image_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")
        
        # 保存分析结果
        output_file = DATA_DIRS["outputs"] / f"{security_report['report_id']}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "report": security_report,
                "analysis_details": analysis_result
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分析完成 - 报告ID: {security_report['report_id']}")
        
        return jsonify({
            "success": True,
            "report": security_report,
            "analysis_metadata": {
                "processing_time": "模拟处理时间",
                "models_used": ["qwen-vl-chat", "paddleocr", "yolov8"],
                "output_file": str(output_file)
            }
        })
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        return jsonify({
            "success": False,
            "error": "分析失败",
            "message": str(e)
        }), 500

@app.route('/api/reports/<report_id>', methods=['GET'])
def get_report(report_id):
    """
    获取分析报告
    ---
    tags:
      - Reports
    parameters:
      - name: report_id
        in: path
        type: string
        required: true
        description: 报告ID
    responses:
      200:
        description: 报告获取成功
      404:
        description: 报告不存在
    """
    try:
        report_file = DATA_DIRS["outputs"] / f"{report_id}.json"
        
        if not report_file.exists():
            return jsonify({
                "success": False,
                "error": "报告不存在"
            }), 404
        
        with open(report_file, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        return jsonify({
            "success": True,
            "data": report_data
        })
        
    except Exception as e:
        logger.error(f"获取报告失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/reports', methods=['GET'])
def list_reports():
    """
    获取报告列表
    ---
    tags:
      - Reports
    responses:
      200:
        description: 报告列表获取成功
    """
    try:
        reports = []
        for report_file in DATA_DIRS["outputs"].glob("*.json"):
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    report = data.get("report", {})
                    reports.append({
                        "report_id": report.get("report_id"),
                        "threat_type": report.get("threat_type"),
                        "severity_level": report.get("severity_level"),
                        "generated_at": report.get("generated_at"),
                        "confidence_score": report.get("confidence_score")
                    })
            except Exception as e:
                logger.warning(f"读取报告文件失败 {report_file}: {e}")
                continue
        
        # 按时间排序
        reports.sort(key=lambda x: x.get("generated_at", ""), reverse=True)
        
        return jsonify({
            "success": True,
            "reports": reports,
            "total": len(reports)
        })
        
    except Exception as e:
        logger.error(f"获取报告列表失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    logger.info("启动 SecVizLM 服务...")
    logger.info(f"API 文档地址: http://{FLASK_CONFIG['host']}:{FLASK_CONFIG['port']}/docs/")
    
    app.run(
        host=FLASK_CONFIG["host"],
        port=FLASK_CONFIG["port"],
        debug=FLASK_CONFIG["debug"],
        threaded=FLASK_CONFIG["threaded"]
    )
