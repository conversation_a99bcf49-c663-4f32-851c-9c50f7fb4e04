#!/usr/bin/env python3
"""
SecVizLM 系统测试脚本
用于验证各个模块的功能
"""

import unittest
import json
import tempfile
import os
from pathlib import Path

# 导入项目模块
from security_utils import Security<PERSON>nput<PERSON>ilter, ThreatAnalyzer, SecurityReportGenerator
from multimodal_engine import MultiModalEngine
from config import SECURITY_CONFIG

class TestSecurityUtils(unittest.TestCase):
    """测试安全工具模块"""
    
    def setUp(self):
        self.filter = SecurityInputFilter()
        self.analyzer = ThreatAnalyzer()
        self.generator = SecurityReportGenerator()
    
    def test_input_filter(self):
        """测试输入过滤"""
        # 测试正常文本
        normal_text = "This is a normal log entry"
        filtered = self.filter.sanitize_text_input(normal_text)
        self.assertEqual(filtered, normal_text)
        
        # 测试危险脚本
        malicious_text = "<script>alert('xss')</script>"
        filtered = self.filter.sanitize_text_input(malicious_text)
        self.assertIn("[FILTERED]", filtered)
        
        print("✅ 输入过滤测试通过")
    
    def test_threat_analyzer(self):
        """测试威胁分析"""
        # SQL 注入日志
        sql_injection_log = """
        ************* - - [09/Jan/2025:14:22:30] "GET /login.php?id=1' UNION SELECT * FROM users-- HTTP/1.1" 200 1234
        """
        
        result = self.analyzer.analyze_log_content(sql_injection_log)
        
        self.assertIn("SQL注入攻击", result["threats"])
        self.assertTrue(len(result["indicators"]) > 0)
        self.assertTrue(len(result["assets"]) > 0)
        
        print("✅ 威胁分析测试通过")
        print(f"   检测到威胁: {result['threats']}")
        print(f"   检测到资产: {result['assets']}")
    
    def test_report_generator(self):
        """测试报告生成"""
        # 模拟威胁分析结果
        threat_analysis = {
            "threats": ["SQL注入攻击"],
            "indicators": ["检测到模式: union\\s+select"],
            "timeline": "2025-01-09 14:22:30",
            "assets": ["服务器(*************)"]
        }
        
        report = self.generator.generate_report(threat_analysis)
        
        self.assertEqual(report["threat_type"], "SQL注入攻击")
        self.assertIn("服务器(*************)", report["affected_assets"])
        self.assertIn("Critical", ["Critical", "High", "Medium", "Low"])
        self.assertTrue(0 <= report["confidence_score"] <= 1)
        
        print("✅ 报告生成测试通过")
        print(f"   报告ID: {report['report_id']}")
        print(f"   威胁类型: {report['threat_type']}")
        print(f"   严重程度: {report['severity_level']}")

class TestMultiModalEngine(unittest.TestCase):
    """测试多模态引擎"""
    
    def setUp(self):
        self.engine = MultiModalEngine()
    
    def test_text_analysis(self):
        """测试文本分析"""
        sample_log = """
        ************* - - [09/Jan/2025:14:22:30] "GET /login.php?id=1' OR 1=1-- HTTP/1.1" 200 1234
        ************ - - [09/Jan/2025:14:25:15] "GET /search.php?q=<script>alert('XSS')</script> HTTP/1.1" 200 2048
        """
        
        result = self.engine._analyze_text(sample_log)
        
        self.assertIn("threat_analysis", result)
        self.assertIn("key_information", result)
        
        threats = result["threat_analysis"]["threats"]
        self.assertTrue(len(threats) > 0)
        
        print("✅ 文本分析测试通过")
        print(f"   检测到威胁数量: {len(threats)}")
    
    def test_image_metadata_extraction(self):
        """测试图像元数据提取"""
        # 创建一个临时图像文件进行测试
        import numpy as np
        import cv2
        
        # 创建一个简单的测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:, :] = [255, 0, 0]  # 红色图像
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            cv2.imwrite(tmp_file.name, test_image)
            
            try:
                metadata = self.engine._extract_image_metadata(test_image, tmp_file.name)
                
                self.assertEqual(metadata["dimensions"], "100x100")
                self.assertEqual(metadata["channels"], 3)
                self.assertEqual(metadata["format"], ".png")
                
                print("✅ 图像元数据提取测试通过")
                print(f"   图像尺寸: {metadata['dimensions']}")
                print(f"   颜色通道: {metadata['channels']}")
                
            finally:
                os.unlink(tmp_file.name)

class TestConfiguration(unittest.TestCase):
    """测试配置模块"""
    
    def test_security_config(self):
        """测试安全配置"""
        self.assertIn("threat_categories", SECURITY_CONFIG)
        self.assertIn("input_filters", SECURITY_CONFIG)
        self.assertIn("report_template", SECURITY_CONFIG)
        
        # 检查威胁类别
        categories = SECURITY_CONFIG["threat_categories"]
        self.assertIn("SQL注入攻击", categories)
        self.assertIn("XSS攻击", categories)
        
        print("✅ 安全配置测试通过")
        print(f"   威胁类别数量: {len(categories)}")

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_full_analysis_pipeline(self):
        """测试完整分析流程"""
        # 准备测试数据
        sample_log = """
        ************* - - [09/Jan/2025:14:22:30] "GET /login.php?id=1' UNION SELECT * FROM users-- HTTP/1.1" 200 1234
        ************* - - [09/Jan/2025:14:22:35] "POST /admin.php HTTP/1.1" 403 567
        """
        
        # 初始化组件
        security_filter = SecurityInputFilter()
        multimodal_engine = MultiModalEngine()
        report_generator = SecurityReportGenerator()
        
        # 执行分析流程
        try:
            # 1. 输入过滤
            filtered_log = security_filter.sanitize_text_input(sample_log)
            
            # 2. 多模态分析
            analysis_result = multimodal_engine.process_multimodal_input(
                text_input=filtered_log
            )
            
            # 3. 生成报告
            threat_analysis = analysis_result.get("text_analysis", {}).get("threat_analysis", {})
            report = report_generator.generate_report(threat_analysis)
            
            # 验证结果
            self.assertIsNotNone(report["report_id"])
            self.assertIn(report["severity_level"], ["Critical", "High", "Medium", "Low"])
            
            print("✅ 完整分析流程测试通过")
            print(f"   生成报告ID: {report['report_id']}")
            print(f"   威胁类型: {report['threat_type']}")
            print(f"   严重程度: {report['severity_level']}")
            print(f"   置信度: {report['confidence_score']}")
            
        except Exception as e:
            self.fail(f"完整分析流程测试失败: {e}")

def run_performance_test():
    """性能测试"""
    print("\n🚀 性能测试")
    print("-" * 40)
    
    import time
    
    # 测试大量日志处理
    large_log = """
    ************* - - [09/Jan/2025:14:22:30] "GET /login.php?id=1' UNION SELECT * FROM users-- HTTP/1.1" 200 1234
    """ * 100  # 重复100次
    
    analyzer = ThreatAnalyzer()
    
    start_time = time.time()
    result = analyzer.analyze_log_content(large_log)
    end_time = time.time()
    
    processing_time = end_time - start_time
    log_size = len(large_log.encode('utf-8'))
    
    print(f"✅ 处理 {log_size:,} 字节日志")
    print(f"   处理时间: {processing_time:.2f} 秒")
    print(f"   处理速度: {log_size/processing_time/1024:.2f} KB/s")
    print(f"   检测威胁: {len(result['threats'])} 种")

def main():
    """主测试函数"""
    print("🧪 SecVizLM 系统测试")
    print("=" * 50)
    
    # 运行单元测试
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestSecurityUtils))
    test_suite.addTest(unittest.makeSuite(TestMultiModalEngine))
    test_suite.addTest(unittest.makeSuite(TestConfiguration))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0)
    result = runner.run(test_suite)
    
    # 运行性能测试
    run_performance_test()
    
    # 测试总结
    print("\n📊 测试总结")
    print("-" * 40)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}")
    
    if result.wasSuccessful():
        print("\n🎉 所有测试通过！系统运行正常。")
    else:
        print("\n⚠️  部分测试失败，请检查系统配置。")

if __name__ == "__main__":
    main()
