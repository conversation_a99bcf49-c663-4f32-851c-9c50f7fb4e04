#!/usr/bin/env python3
"""
SecVizLM 演示启动脚本
用于毕业设计展示和测试
"""

import os
import sys
import time
import requests
import json
from pathlib import Path

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'flask', 'torch', 'transformers', 'opencv-python', 
        'paddleocr', 'ultralytics', 'flasgger'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def start_server():
    """启动 Flask 服务器"""
    print("🚀 启动 SecVizLM 服务器...")
    
    # 检查端口是否被占用
    try:
        response = requests.get("http://localhost:5000/health", timeout=2)
        print("⚠️  服务器已在运行中")
        return True
    except requests.exceptions.RequestException:
        pass
    
    # 启动服务器
    os.system("python app.py")

def test_api():
    """测试 API 接口"""
    print("\n🧪 测试 API 接口...")
    
    base_url = "http://localhost:5000"
    
    # 等待服务器启动
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/health", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器启动成功")
                break
        except requests.exceptions.RequestException:
            print(f"⏳ 等待服务器启动... ({i+1}/10)")
            time.sleep(2)
    else:
        print("❌ 服务器启动失败")
        return False
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health")
        print(f"✅ 健康检查: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
    
    # 测试文本分析
    try:
        sample_log = """
        ************* - - [09/Jan/2025:14:22:30 +0800] "GET /login.php?id=1' UNION SELECT * FROM users-- HTTP/1.1" 200 1234
        """
        
        data = {"log_text": sample_log.strip()}
        response = requests.post(f"{base_url}/api/analyze", data=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文本分析测试成功")
            print(f"   威胁类型: {result.get('report', {}).get('threat_type', 'N/A')}")
            print(f"   严重程度: {result.get('report', {}).get('severity_level', 'N/A')}")
        else:
            print(f"❌ 文本分析测试失败: {response.status_code}")
            print(f"   响应: {response.text}")
    
    except Exception as e:
        print(f"❌ API 测试失败: {e}")
    
    return True

def show_demo_info():
    """显示演示信息"""
    print("\n" + "="*60)
    print("🛡️  SecVizLM 多模态安全事件分析报告生成系统")
    print("="*60)
    print("📖 访问地址:")
    print("   主页:     http://localhost:5000")
    print("   API文档:  http://localhost:5000/docs/")
    print("   健康检查: http://localhost:5000/health")
    print("\n🧪 测试命令:")
    print("   # 文本分析")
    print("   curl -X POST http://localhost:5000/api/analyze \\")
    print("     -F \"log_text=************* - - [09/Jan/2025:14:22:30] 'GET /login.php?id=1' UNION SELECT * FROM users--'\"")
    print("\n   # 查看报告列表")
    print("   curl http://localhost:5000/api/reports")
    print("\n📁 测试文件:")
    print("   test_samples/sample_log.txt - 包含多种攻击类型的模拟日志")
    print("="*60)

def main():
    """主函数"""
    print("🎓 SecVizLM 毕业设计演示系统")
    print("多模态安全事件分析报告生成系统\n")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 显示演示信息
    show_demo_info()
    
    # 询问用户操作
    print("\n请选择操作:")
    print("1. 启动服务器")
    print("2. 测试 API")
    print("3. 启动服务器并测试")
    print("4. 退出")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        start_server()
    elif choice == "2":
        test_api()
    elif choice == "3":
        # 在后台启动服务器
        import subprocess
        import threading
        
        def run_server():
            subprocess.run([sys.executable, "app.py"])
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # 等待一下然后测试
        time.sleep(3)
        test_api()
        
        print("\n按 Ctrl+C 停止服务器")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")
    elif choice == "4":
        print("👋 再见!")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
