"""
SecVizLM 安全工具模块
输入清洗、报告模板、OWASP 威胁分析
"""

import re
import json
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from config import SECURITY_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityInputFilter:
    """安全输入过滤器"""
    
    def __init__(self):
        self.blocked_patterns = SECURITY_CONFIG["input_filters"]["blocked_patterns"]
        self.max_log_size = SECURITY_CONFIG["input_filters"]["max_log_size"]
        self.max_image_size = SECURITY_CONFIG["input_filters"]["max_image_size"]
    
    def sanitize_text_input(self, text: str) -> str:
        """清洗文本输入"""
        if not text:
            return ""
        
        # 检查大小限制
        if len(text.encode('utf-8')) > self.max_log_size:
            raise ValueError(f"文本输入超过大小限制: {self.max_log_size} bytes")
        
        # 移除危险模式
        sanitized = text
        for pattern in self.blocked_patterns:
            sanitized = re.sub(pattern, "[FILTERED]", sanitized, flags=re.IGNORECASE)
        
        # 基本HTML转义
        sanitized = sanitized.replace("<", "&lt;").replace(">", "&gt;")
        
        logger.info(f"文本输入已清洗，原长度: {len(text)}, 清洗后: {len(sanitized)}")
        return sanitized
    
    def validate_image_upload(self, file_path: Path) -> bool:
        """验证图像上传"""
        if not file_path.exists():
            return False
        
        # 检查文件大小
        file_size = file_path.stat().st_size
        if file_size > self.max_image_size:
            raise ValueError(f"图像文件超过大小限制: {self.max_image_size} bytes")
        
        # 检查文件头（魔数）
        with open(file_path, 'rb') as f:
            header = f.read(8)
        
        # 常见图像格式的魔数
        image_signatures = {
            b'\x89PNG\r\n\x1a\n': 'PNG',
            b'\xff\xd8\xff': 'JPEG',
            b'GIF87a': 'GIF',
            b'GIF89a': 'GIF',
            b'BM': 'BMP'
        }
        
        for signature, format_name in image_signatures.items():
            if header.startswith(signature):
                logger.info(f"验证通过的图像格式: {format_name}")
                return True
        
        raise ValueError("不支持的图像格式或文件损坏")

class ThreatAnalyzer:
    """威胁分析器"""
    
    def __init__(self):
        self.threat_categories = SECURITY_CONFIG["threat_categories"]
        self.threat_patterns = self._load_threat_patterns()
    
    def _load_threat_patterns(self) -> Dict[str, List[str]]:
        """加载威胁检测模式"""
        return {
            "SQL注入攻击": [
                r"union\s+select", r"or\s+1\s*=\s*1", r"drop\s+table",
                r"insert\s+into", r"delete\s+from", r"update\s+.*set",
                r"exec\s*\(", r"sp_executesql"
            ],
            "XSS攻击": [
                r"<script.*?>", r"javascript:", r"vbscript:",
                r"onload\s*=", r"onerror\s*=", r"onclick\s*="
            ],
            "目录遍历": [
                r"\.\.\/", r"\.\.\\", r"%2e%2e%2f", r"%2e%2e%5c"
            ],
            "命令注入": [
                r";\s*cat\s+", r";\s*ls\s+", r";\s*dir\s+",
                r"&&\s*", r"\|\|\s*", r"`.*`"
            ],
            "暴力破解": [
                r"admin.*admin", r"password.*123", r"login.*failed",
                r"authentication.*failed", r"invalid.*credentials"
            ]
        }
    
    def analyze_log_content(self, log_content: str) -> Dict[str, Any]:
        """分析日志内容中的威胁"""
        threats_found = []
        indicators = []
        
        for threat_type, patterns in self.threat_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, log_content, re.IGNORECASE)
                if matches:
                    threats_found.append(threat_type)
                    indicators.extend([f"检测到模式: {pattern}" for _ in matches])
        
        # 提取时间戳
        timeline = self._extract_timeline(log_content)
        
        # 提取IP地址
        affected_assets = self._extract_assets(log_content)
        
        return {
            "threats": list(set(threats_found)),
            "indicators": list(set(indicators)),
            "timeline": timeline,
            "assets": affected_assets
        }
    
    def _extract_timeline(self, log_content: str) -> str:
        """提取攻击时间线"""
        # 常见日志时间格式
        time_patterns = [
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',  # 2025-01-09 14:22:30
            r'\d{2}/\w{3}/\d{4}:\d{2}:\d{2}:\d{2}',    # 09/Jan/2025:14:22:30
            r'\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}'     # Jan  9 14:22:30
        ]
        
        timestamps = []
        for pattern in time_patterns:
            matches = re.findall(pattern, log_content)
            timestamps.extend(matches)
        
        if timestamps:
            return f"{timestamps[0]} → {timestamps[-1]}" if len(timestamps) > 1 else timestamps[0]
        return "时间戳未找到"
    
    def _extract_assets(self, log_content: str) -> List[str]:
        """提取受影响的资产"""
        assets = []
        
        # IP地址模式
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ips = re.findall(ip_pattern, log_content)
        
        # 域名模式
        domain_pattern = r'\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\b'
        domains = re.findall(domain_pattern, log_content)
        
        for ip in set(ips):
            if self._is_valid_ip(ip):
                assets.append(f"服务器({ip})")
        
        return assets[:5]  # 限制返回数量
    
    def _is_valid_ip(self, ip: str) -> bool:
        """验证IP地址有效性"""
        parts = ip.split('.')
        if len(parts) != 4:
            return False
        try:
            return all(0 <= int(part) <= 255 for part in parts)
        except ValueError:
            return False

class SecurityReportGenerator:
    """安全报告生成器"""
    
    def __init__(self):
        self.template = SECURITY_CONFIG["report_template"].copy()
    
    def generate_report(self, 
                       threat_analysis: Dict[str, Any],
                       multimodal_insights: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成结构化安全报告"""
        
        report = self.template.copy()
        
        # 填充威胁分析结果
        if threat_analysis.get("threats"):
            report["threat_type"] = threat_analysis["threats"][0]  # 主要威胁
        
        report["affected_assets"] = threat_analysis.get("assets", [])
        report["attack_timeline"] = threat_analysis.get("timeline", "")
        report["critical_indicators"] = threat_analysis.get("indicators", [])
        
        # 生成推荐措施
        report["recommended_actions"] = self._generate_recommendations(
            threat_analysis.get("threats", [])
        )
        
        # 计算严重程度
        report["severity_level"] = self._calculate_severity(threat_analysis)
        
        # 置信度评分
        report["confidence_score"] = self._calculate_confidence(threat_analysis)
        
        # 添加多模态洞察
        if multimodal_insights:
            report["additional_context"] = multimodal_insights.get("context", "")
        
        # 添加元数据
        report["generated_at"] = datetime.now().isoformat()
        report["report_id"] = self._generate_report_id()
        
        return report
    
    def _generate_recommendations(self, threats: List[str]) -> List[str]:
        """根据威胁类型生成推荐措施"""
        recommendations = []
        
        threat_actions = {
            "SQL注入攻击": ["使用参数化查询", "输入验证和过滤", "最小权限原则"],
            "XSS攻击": ["输出编码", "内容安全策略(CSP)", "输入验证"],
            "目录遍历": ["路径规范化", "访问控制", "白名单验证"],
            "命令注入": ["避免系统调用", "输入验证", "沙箱执行"],
            "暴力破解": ["账户锁定策略", "强密码策略", "多因素认证"]
        }
        
        for threat in threats:
            if threat in threat_actions:
                recommendations.extend(threat_actions[threat])
        
        return list(set(recommendations))[:5]  # 去重并限制数量
    
    def _calculate_severity(self, threat_analysis: Dict[str, Any]) -> str:
        """计算威胁严重程度"""
        threats = threat_analysis.get("threats", [])
        indicators = threat_analysis.get("indicators", [])
        
        high_risk_threats = ["SQL注入攻击", "命令注入", "权限提升"]
        
        if any(threat in high_risk_threats for threat in threats):
            return "Critical"
        elif len(threats) > 2 or len(indicators) > 5:
            return "High"
        elif len(threats) > 0:
            return "Medium"
        else:
            return "Low"
    
    def _calculate_confidence(self, threat_analysis: Dict[str, Any]) -> float:
        """计算分析置信度"""
        threats = len(threat_analysis.get("threats", []))
        indicators = len(threat_analysis.get("indicators", []))
        assets = len(threat_analysis.get("assets", []))
        
        # 简单的置信度计算
        confidence = min(0.9, (threats * 0.3 + indicators * 0.1 + assets * 0.2))
        return round(confidence, 2)
    
    def _generate_report_id(self) -> str:
        """生成报告ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        hash_suffix = hashlib.md5(timestamp.encode()).hexdigest()[:8]
        return f"SECVIZ_{timestamp}_{hash_suffix}"
