"""
SecVizLM 配置文件
多模态安全事件分析报告生成系统配置
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.absolute()

# 模型配置
MODEL_CONFIG = {
    # Qwen-VL-Chat 模型配置
    "qwen_vl_model": "Qwen/Qwen-VL-Chat",
    "qwen_vl_device": "cuda:0",  # 或 "cpu"
    "qwen_vl_precision": "fp16",
    
    # LoRA 微调配置
    "lora_config": {
        "r": 8,
        "lora_alpha": 32,
        "target_modules": ["c_attn", "c_proj", "w1", "w2"],
        "lora_dropout": 0.1,
        "bias": "none",
        "task_type": "CAUSAL_LM"
    },
    
    # 推理参数
    "generation_config": {
        "max_new_tokens": 1024,
        "temperature": 0.1,
        "top_p": 0.8,
        "do_sample": True,
        "repetition_penalty": 1.1
    }
}

# 计算机视觉配置
CV_CONFIG = {
    # PaddleOCR 配置
    "ocr_config": {
        "use_angle_cls": True,
        "lang": "ch",  # 支持中英文
        "use_gpu": True,
        "show_log": False
    },
    
    # YOLOv8 配置 (用于目标检测)
    "yolo_model": "yolov8n.pt",
    "yolo_confidence": 0.5,
    
    # 图像预处理
    "image_preprocessing": {
        "max_size": (1024, 1024),
        "normalize": True,
        "enhance_contrast": True
    }
}

# Flask 应用配置
FLASK_CONFIG = {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": True,
    "threaded": True,
    
    # 文件上传配置
    "max_content_length": 16 * 1024 * 1024,  # 16MB
    "upload_folder": PROJECT_ROOT / "uploads",
    "allowed_extensions": {"txt", "log", "png", "jpg", "jpeg", "gif", "bmp"}
}

# 安全配置
SECURITY_CONFIG = {
    # OWASP 威胁分类
    "threat_categories": [
        "SQL注入攻击", "XSS攻击", "CSRF攻击", "文件上传漏洞",
        "目录遍历", "命令注入", "身份认证绕过", "权限提升",
        "拒绝服务攻击", "暴力破解", "恶意文件上传", "信息泄露"
    ],
    
    # 输入过滤规则
    "input_filters": {
        "max_log_size": 10 * 1024 * 1024,  # 10MB
        "max_image_size": 5 * 1024 * 1024,   # 5MB
        "blocked_patterns": [
            r"<script.*?>.*?</script>",  # XSS
            r"javascript:",
            r"vbscript:",
            r"onload=",
            r"onerror="
        ]
    },
    
    # 报告模板字段
    "report_template": {
        "threat_type": "",
        "affected_assets": [],
        "attack_timeline": "",
        "critical_indicators": [],
        "recommended_actions": [],
        "severity_level": "",  # Critical/High/Medium/Low
        "confidence_score": 0.0,  # 0-1
        "additional_context": ""
    }
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": PROJECT_ROOT / "logs" / "secvizlm.log"
}

# 数据目录
DATA_DIRS = {
    "uploads": PROJECT_ROOT / "uploads",
    "logs": PROJECT_ROOT / "logs",
    "models": PROJECT_ROOT / "models",
    "outputs": PROJECT_ROOT / "outputs"
}

# 创建必要的目录
for dir_path in DATA_DIRS.values():
    dir_path.mkdir(parents=True, exist_ok=True)

# 环境变量
def get_env_config():
    """获取环境变量配置"""
    return {
        "huggingface_token": os.getenv("HUGGINGFACE_TOKEN"),
        "openai_api_key": os.getenv("OPENAI_API_KEY"),  # 备用API
        "cuda_visible_devices": os.getenv("CUDA_VISIBLE_DEVICES", "0")
    }
