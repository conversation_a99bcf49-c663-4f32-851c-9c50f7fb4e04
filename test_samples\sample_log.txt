# SecVizLM 测试样本 - 模拟安全日志

# 1. SQL 注入攻击日志
************* - - [09/Jan/2025:14:22:30 +0800] "GET /login.php?id=1' UNION SELECT * FROM users-- HTTP/1.1" 200 1234 "-" "Mozilla/5.0"
************* - - [09/Jan/2025:14:22:35 +0800] "POST /admin.php HTTP/1.1" 403 567 "-" "sqlmap/1.6.12"
************* - - [09/Jan/2025:14:22:40 +0800] "GET /users.php?id=1 OR 1=1 HTTP/1.1" 500 0 "-" "Mozilla/5.0"

# 2. XSS 攻击日志
************ - - [09/Jan/2025:14:25:15 +0800] "GET /search.php?q=<script>alert('XSS')</script> HTTP/1.1" 200 2048 "-" "Mozilla/5.0"
************ - - [09/Jan/2025:14:25:20 +0800] "POST /comment.php HTTP/1.1" 200 1024 "-" "Mozilla/5.0"

# 3. 目录遍历攻击
************* - - [09/Jan/2025:14:28:10 +0800] "GET /download.php?file=../../../etc/passwd HTTP/1.1" 403 0 "-" "curl/7.68.0"
************* - - [09/Jan/2025:14:28:15 +0800] "GET /files.php?path=..%2F..%2F..%2Fetc%2Fshadow HTTP/1.1" 403 0 "-" "wget/1.20.3"

# 4. 暴力破解攻击
********* - - [09/Jan/2025:14:30:00 +0800] "POST /login.php HTTP/1.1" 401 256 "-" "hydra/9.1"
********* - - [09/Jan/2025:14:30:01 +0800] "POST /login.php HTTP/1.1" 401 256 "-" "hydra/9.1"
********* - - [09/Jan/2025:14:30:02 +0800] "POST /login.php HTTP/1.1" 401 256 "-" "hydra/9.1"
********* - - [09/Jan/2025:14:30:03 +0800] "POST /login.php HTTP/1.1" 401 256 "-" "hydra/9.1"
********* - - [09/Jan/2025:14:30:04 +0800] "POST /login.php HTTP/1.1" 200 1024 "-" "hydra/9.1"

# 5. 命令注入攻击
************ - - [09/Jan/2025:14:32:30 +0800] "GET /ping.php?host=127.0.0.1;cat /etc/passwd HTTP/1.1" 500 0 "-" "Mozilla/5.0"
************ - - [09/Jan/2025:14:32:35 +0800] "POST /exec.php HTTP/1.1" 500 0 "-" "curl/7.68.0"

# 6. 文件上传漏洞
************* - - [09/Jan/2025:14:35:00 +0800] "POST /upload.php HTTP/1.1" 200 512 "-" "Mozilla/5.0"
************* - - [09/Jan/2025:14:35:05 +0800] "GET /uploads/shell.php HTTP/1.1" 200 2048 "-" "Mozilla/5.0"

# 7. DDoS 攻击模式
************* - - [09/Jan/2025:14:40:00 +0800] "GET / HTTP/1.1" 200 1024 "-" "Mozilla/5.0"
************* - - [09/Jan/2025:14:40:00 +0800] "GET / HTTP/1.1" 200 1024 "-" "Mozilla/5.0"
************* - - [09/Jan/2025:14:40:00 +0800] "GET / HTTP/1.1" 200 1024 "-" "Mozilla/5.0"
************* - - [09/Jan/2025:14:40:00 +0800] "GET / HTTP/1.1" 200 1024 "-" "Mozilla/5.0"
************* - - [09/Jan/2025:14:40:00 +0800] "GET / HTTP/1.1" 200 1024 "-" "Mozilla/5.0"

# 8. 异常用户代理
************* - - [09/Jan/2025:14:45:00 +0800] "GET /robots.txt HTTP/1.1" 200 128 "-" "Nikto/2.1.6"
************* - - [09/Jan/2025:14:45:05 +0800] "GET /admin/ HTTP/1.1" 403 0 "-" "dirb/2.22"
************* - - [09/Jan/2025:14:45:10 +0800] "GET /.git/config HTTP/1.1" 404 0 "-" "GitTools/0.1"

# 9. 防火墙告警日志
[2025-01-09 14:50:00] FIREWALL: BLOCKED - Source: *************, Destination: ************:22, Protocol: TCP, Reason: Brute Force Detection
[2025-01-09 14:50:05] FIREWALL: BLOCKED - Source: **************, Destination: ************:3389, Protocol: TCP, Reason: Port Scan Detection
[2025-01-09 14:50:10] FIREWALL: BLOCKED - Source: **********, Destination: ***********/24, Protocol: ICMP, Reason: Ping Flood

# 10. IDS/IPS 告警
[2025-01-09 14:55:00] IDS ALERT: Possible SQL Injection - Source: *************, Target: web-server-01, Signature: "UNION SELECT"
[2025-01-09 14:55:05] IPS BLOCK: XSS Attack Blocked - Source: ************, Target: web-server-01, Signature: "<script>"
[2025-01-09 14:55:10] IDS ALERT: Directory Traversal - Source: *************, Target: file-server-01, Signature: "../../../"

# 测试说明：
# 这个日志文件包含了多种常见的网络安全威胁模式
# 用于测试 SecVizLM 系统的威胁检测和分析能力
# 包括：SQL注入、XSS、目录遍历、暴力破解、命令注入、文件上传、DDoS等
