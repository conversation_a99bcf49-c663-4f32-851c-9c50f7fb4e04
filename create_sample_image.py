#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create sample images for testing
Simulate security alert screenshots
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_alert_image():
    """创建模拟的安全告警截图"""
    
    # 创建画布
    width, height = 800, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 240  # 浅灰色背景
    
    # 转换为 PIL 图像以便添加文字
    pil_image = Image.fromarray(image)
    draw = ImageDraw.Draw(pil_image)
    
    try:
        # 尝试使用系统字体
        font_large = ImageFont.truetype("arial.ttf", 24)
        font_medium = ImageFont.truetype("arial.ttf", 16)
        font_small = ImageFont.truetype("arial.ttf", 12)
    except:
        # 如果没有找到字体，使用默认字体
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # 绘制标题栏
    draw.rectangle([0, 0, width, 60], fill=(220, 53, 69))  # 红色警告栏
    draw.text((20, 20), "🚨 SECURITY ALERT - SQL Injection Detected", 
              fill=(255, 255, 255), font=font_large)
    
    # 绘制主要信息区域
    y_offset = 80
    
    # 威胁信息
    draw.rectangle([20, y_offset, width-20, y_offset+120], 
                   fill=(255, 255, 255), outline=(200, 200, 200))
    
    draw.text((40, y_offset+10), "Threat Type: SQL Injection Attack", 
              fill=(220, 53, 69), font=font_medium)
    draw.text((40, y_offset+35), "Source IP: ************0", 
              fill=(0, 0, 0), font=font_medium)
    draw.text((40, y_offset+60), "Target: web-server-01 (************)", 
              fill=(0, 0, 0), font=font_medium)
    draw.text((40, y_offset+85), "Time: 2025-01-09 14:22:30", 
              fill=(0, 0, 0), font=font_medium)
    
    y_offset += 140
    
    # 攻击详情
    draw.rectangle([20, y_offset, width-20, y_offset+150], 
                   fill=(255, 255, 255), outline=(200, 200, 200))
    
    draw.text((40, y_offset+10), "Attack Details:", 
              fill=(0, 0, 0), font=font_medium)
    draw.text((40, y_offset+35), "GET /login.php?id=1' UNION SELECT * FROM users--", 
              fill=(220, 53, 69), font=font_small)
    draw.text((40, y_offset+55), "Response Code: 200", 
              fill=(0, 0, 0), font=font_small)
    draw.text((40, y_offset+75), "Payload Size: 1234 bytes", 
              fill=(0, 0, 0), font=font_small)
    draw.text((40, y_offset+95), "User Agent: sqlmap/1.6.12", 
              fill=(220, 53, 69), font=font_small)
    draw.text((40, y_offset+115), "Risk Level: CRITICAL", 
              fill=(220, 53, 69), font=font_medium)
    
    y_offset += 170
    
    # 推荐措施
    draw.rectangle([20, y_offset, width-20, y_offset+100], 
                   fill=(255, 255, 255), outline=(200, 200, 200))
    
    draw.text((40, y_offset+10), "Recommended Actions:", 
              fill=(0, 0, 0), font=font_medium)
    draw.text((40, y_offset+35), "• Block source IP immediately", 
              fill=(0, 0, 0), font=font_small)
    draw.text((40, y_offset+55), "• Review and patch SQL injection vulnerabilities", 
              fill=(0, 0, 0), font=font_small)
    draw.text((40, y_offset+75), "• Enable WAF protection", 
              fill=(0, 0, 0), font=font_small)
    
    # 转换回 OpenCV 格式
    cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    return cv_image

def create_network_topology_image():
    """创建模拟的网络拓扑图"""
    
    width, height = 800, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 绘制网络节点
    # 互联网
    cv2.circle(image, (100, 100), 40, (100, 100, 100), -1)
    cv2.putText(image, "Internet", (60, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # 防火墙
    cv2.rectangle(image, (200, 80), (280, 120), (255, 0, 0), -1)
    cv2.putText(image, "Firewall", (205, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(image, "192.168.1.1", (200, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
    
    # Web 服务器
    cv2.rectangle(image, (400, 80), (480, 120), (0, 255, 0), -1)
    cv2.putText(image, "Web Server", (405, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(image, "************", (400, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
    
    # 攻击者
    cv2.circle(image, (100, 300), 40, (0, 0, 255), -1)
    cv2.putText(image, "Attacker", (60, 360), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    cv2.putText(image, "203.0.113.100", (40, 380), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
    
    # 绘制连接线
    cv2.line(image, (140, 100), (200, 100), (0, 0, 0), 2)
    cv2.line(image, (280, 100), (400, 100), (0, 0, 0), 2)
    cv2.line(image, (140, 300), (200, 120), (255, 0, 0), 3)  # 攻击路径
    
    # 添加攻击标识
    cv2.putText(image, "SQL Injection Attack", (200, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
    cv2.arrowedLine(image, (180, 260), (220, 130), (255, 0, 0), 3)
    
    return image

def main():
    """Main function"""
    print("Creating sample images for testing...")

    # Ensure directory exists
    os.makedirs("test_samples", exist_ok=True)

    try:
        # Create security alert screenshot
        alert_image = create_sample_alert_image()
        cv2.imwrite("test_samples/sample_alert.png", alert_image)
        print("✅ Created security alert screenshot: test_samples/sample_alert.png")

        # Create network topology diagram
        topology_image = create_network_topology_image()
        cv2.imwrite("test_samples/network_topology.png", topology_image)
        print("✅ Created network topology diagram: test_samples/network_topology.png")

        # Create a simple text image for OCR testing
        text_image = np.ones((200, 600, 3), dtype=np.uint8) * 255
        cv2.putText(text_image, "CRITICAL ALERT: SQL Injection Detected",
                    (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        cv2.putText(text_image, "Source: ************0",
                    (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        cv2.putText(text_image, "Target: web-server-01",
                    (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

        cv2.imwrite("test_samples/ocr_test.png", text_image)
        print("✅ Created OCR test image: test_samples/ocr_test.png")

        print("\n📁 Test images created successfully!")
        print("   These images can be used to test SecVizLM image analysis features")

    except ImportError as e:
        print(f"❌ Missing required packages: {e}")
        print("   Please install: pip install opencv-python pillow numpy")
    except Exception as e:
        print(f"❌ Error creating images: {e}")

if __name__ == "__main__":
    main()
