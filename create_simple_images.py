#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create simple test images using only OpenCV and NumPy
For SecVizLM testing purposes
"""

import cv2
import numpy as np
import os

def create_simple_alert_image():
    """Create a simple security alert image using only OpenCV"""
    
    # Create canvas
    width, height = 800, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 240  # Light gray background
    
    # Draw title bar
    cv2.rectangle(image, (0, 0), (width, 60), (69, 53, 220), -1)  # Red warning bar
    cv2.putText(image, "SECURITY ALERT - SQL Injection Detected", 
                (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # Draw main information area
    y_offset = 80
    
    # Threat information box
    cv2.rectangle(image, (20, y_offset), (width-20, y_offset+120), (255, 255, 255), -1)
    cv2.rectangle(image, (20, y_offset), (width-20, y_offset+120), (200, 200, 200), 2)
    
    cv2.putText(image, "Threat Type: SQL Injection Attack", 
                (40, y_offset+25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (220, 53, 69), 2)
    cv2.putText(image, "Source IP: ************0", 
                (40, y_offset+50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "Target: web-server-01 (************)", 
                (40, y_offset+75), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "Time: 2025-01-09 14:22:30", 
                (40, y_offset+100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    y_offset += 140
    
    # Attack details box
    cv2.rectangle(image, (20, y_offset), (width-20, y_offset+150), (255, 255, 255), -1)
    cv2.rectangle(image, (20, y_offset), (width-20, y_offset+150), (200, 200, 200), 2)
    
    cv2.putText(image, "Attack Details:", 
                (40, y_offset+25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    cv2.putText(image, "GET /login.php?id=1' UNION SELECT * FROM users--", 
                (40, y_offset+50), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (220, 53, 69), 1)
    cv2.putText(image, "Response Code: 200", 
                (40, y_offset+75), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(image, "Payload Size: 1234 bytes", 
                (40, y_offset+100), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(image, "Risk Level: CRITICAL", 
                (40, y_offset+125), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (220, 53, 69), 2)
    
    y_offset += 170
    
    # Recommendations box
    cv2.rectangle(image, (20, y_offset), (width-20, y_offset+100), (255, 255, 255), -1)
    cv2.rectangle(image, (20, y_offset), (width-20, y_offset+100), (200, 200, 200), 2)
    
    cv2.putText(image, "Recommended Actions:", 
                (40, y_offset+25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    cv2.putText(image, "- Block source IP immediately", 
                (40, y_offset+50), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(image, "- Review SQL injection vulnerabilities", 
                (40, y_offset+75), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    return image

def create_network_topology_image():
    """Create a simple network topology diagram"""
    
    width, height = 800, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # White background
    
    # Draw network nodes
    # Internet
    cv2.circle(image, (100, 100), 40, (100, 100, 100), -1)
    cv2.putText(image, "Internet", (60, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # Firewall
    cv2.rectangle(image, (200, 80), (280, 120), (0, 0, 255), -1)
    cv2.putText(image, "Firewall", (205, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(image, "***********", (200, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
    
    # Web Server
    cv2.rectangle(image, (400, 80), (480, 120), (0, 255, 0), -1)
    cv2.putText(image, "Web Server", (405, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(image, "************", (400, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
    
    # Attacker
    cv2.circle(image, (100, 300), 40, (0, 0, 255), -1)
    cv2.putText(image, "Attacker", (60, 360), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    cv2.putText(image, "203.0.113.100", (40, 380), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
    
    # Draw connection lines
    cv2.line(image, (140, 100), (200, 100), (0, 0, 0), 2)
    cv2.line(image, (280, 100), (400, 100), (0, 0, 0), 2)
    cv2.line(image, (140, 300), (200, 120), (0, 0, 255), 3)  # Attack path
    
    # Add attack label
    cv2.putText(image, "SQL Injection Attack", (200, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
    cv2.arrowedLine(image, (180, 260), (220, 130), (0, 0, 255), 3)
    
    return image

def create_ocr_test_image():
    """Create a simple text image for OCR testing"""
    
    width, height = 600, 200
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # White background
    
    # Add text for OCR testing
    cv2.putText(image, "CRITICAL ALERT: SQL Injection Detected", 
                (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    cv2.putText(image, "Source: ************0", 
                (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    cv2.putText(image, "Target: web-server-01", 
                (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    return image

def create_dashboard_screenshot():
    """Create a mock security dashboard screenshot"""
    
    width, height = 1024, 768
    image = np.ones((height, width, 3), dtype=np.uint8) * 245  # Light background
    
    # Title bar
    cv2.rectangle(image, (0, 0), (width, 80), (52, 58, 64), -1)
    cv2.putText(image, "Security Operations Center Dashboard", 
                (20, 45), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
    
    # Statistics boxes
    boxes = [
        {"pos": (50, 120), "size": (200, 100), "title": "Active Threats", "value": "23", "color": (220, 53, 69)},
        {"pos": (300, 120), "size": (200, 100), "title": "Blocked IPs", "value": "156", "color": (255, 193, 7)},
        {"pos": (550, 120), "size": (200, 100), "title": "Clean Traffic", "value": "98.2%", "color": (40, 167, 69)},
        {"pos": (800, 120), "size": (200, 100), "title": "System Status", "value": "Online", "color": (40, 167, 69)}
    ]
    
    for box in boxes:
        x, y = box["pos"]
        w, h = box["size"]
        
        # Draw box
        cv2.rectangle(image, (x, y), (x+w, y+h), (255, 255, 255), -1)
        cv2.rectangle(image, (x, y), (x+w, y+h), (200, 200, 200), 2)
        
        # Add title
        cv2.putText(image, box["title"], (x+10, y+25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        # Add value
        cv2.putText(image, box["value"], (x+10, y+65), cv2.FONT_HERSHEY_SIMPLEX, 1.0, box["color"], 2)
    
    # Recent alerts section
    cv2.rectangle(image, (50, 250), (width-50, 500), (255, 255, 255), -1)
    cv2.rectangle(image, (50, 250), (width-50, 500), (200, 200, 200), 2)
    
    cv2.putText(image, "Recent Security Alerts", (70, 280), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    # Alert entries
    alerts = [
        "14:22:30 - SQL Injection from ************0 - CRITICAL",
        "14:20:15 - XSS Attack from 203.0.113.50 - HIGH", 
        "14:18:45 - Brute Force from 10.0.0.50 - MEDIUM",
        "14:15:30 - Port Scan from 198.51.100.25 - LOW"
    ]
    
    for i, alert in enumerate(alerts):
        y_pos = 320 + i * 30
        color = (220, 53, 69) if "CRITICAL" in alert else (255, 193, 7) if "HIGH" in alert else (0, 123, 255)
        cv2.putText(image, alert, (70, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    return image

def main():
    """Main function to create all test images"""
    print("Creating test images for SecVizLM...")
    
    # Ensure directory exists
    os.makedirs("test_samples", exist_ok=True)
    
    try:
        # Create security alert screenshot
        alert_image = create_simple_alert_image()
        cv2.imwrite("test_samples/sample_alert.png", alert_image)
        print("Created: test_samples/sample_alert.png")

        # Create network topology diagram
        topology_image = create_network_topology_image()
        cv2.imwrite("test_samples/network_topology.png", topology_image)
        print("Created: test_samples/network_topology.png")

        # Create OCR test image
        ocr_image = create_ocr_test_image()
        cv2.imwrite("test_samples/ocr_test.png", ocr_image)
        print("Created: test_samples/ocr_test.png")

        # Create dashboard screenshot
        dashboard_image = create_dashboard_screenshot()
        cv2.imwrite("test_samples/dashboard_screenshot.png", dashboard_image)
        print("Created: test_samples/dashboard_screenshot.png")

        print("\nAll test images created successfully!")
        print("These images can be used to test SecVizLM's multimodal analysis capabilities")

    except Exception as e:
        print("Error creating images: " + str(e))
        print("Make sure opencv-python and numpy are installed:")
        print("pip install opencv-python numpy")

if __name__ == "__main__":
    main()
