# 🛡️ SecVizLM：多模态安全事件分析报告生成系统

> **毕业设计项目** - 基于多模态大语言模型的智能安全分析系统

## 📋 项目概述

SecVizLM 是一个创新的多模态安全事件分析系统，结合了最新的大语言模型技术和计算机视觉技术，能够同时处理文本日志和图像数据，自动生成结构化的安全分析报告。

### 🎯 核心功能

- **多模态输入处理**：支持安全日志文本 + 告警截图的联合分析
- **智能威胁识别**：基于 OWASP 标准的威胁分类和检测
- **结构化报告生成**：自动生成包含威胁类型、影响资产、时间线等信息的 JSON 报告
- **实时 API 服务**：提供 RESTful API 接口和 Swagger 文档

### 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   文本日志      │    │   告警截图      │    │   API 接口      │
│   (Nginx/FW)    │    │   (网络拓扑)    │    │   (Flask)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    SecVizLM 多模态引擎                          │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   文本分析      │   图像分析      │      多模态融合             │
│   威胁检测      │   OCR + YOLO    │      Qwen-VL-Chat          │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  结构化安全报告  │
                    │  (JSON 格式)    │
                    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- CUDA 11.8+ (可选，用于 GPU 加速)
- 8GB+ RAM (推荐 16GB+)

### 安装步骤

1. **克隆项目**
```bash
git clone <your-repo-url>
cd graduate-design
```

2. **创建虚拟环境**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量** (可选)
```bash
# 创建 .env 文件
echo "HUGGINGFACE_TOKEN=your_token_here" > .env
echo "CUDA_VISIBLE_DEVICES=0" >> .env
```

5. **启动服务**
```bash
python app.py
```

6. **访问服务**
- 主页：http://localhost:5000
- API 文档：http://localhost:5000/docs/
- 健康检查：http://localhost:5000/health

## 📖 API 使用指南

### 分析安全事件

**POST** `/api/analyze`

```bash
curl -X POST http://localhost:5000/api/analyze \
  -F "log_text=************* - - [09/Jan/2025:14:22:30] \"GET /login.php?id=1' UNION SELECT * FROM users-- HTTP/1.1\" 200 1234" \
  -F "alert_image=@screenshot.png"
```

**响应示例：**
```json
{
  "success": true,
  "report": {
    "threat_type": "SQL注入攻击",
    "affected_assets": ["服务器(*************)"],
    "attack_timeline": "2025-01-09 14:22:30",
    "critical_indicators": ["检测到模式: union\\s+select"],
    "recommended_actions": ["使用参数化查询", "输入验证和过滤"],
    "severity_level": "Critical",
    "confidence_score": 0.85,
    "report_id": "SECVIZ_20250109_142230_abc12345"
  }
}
```

### 获取报告列表

**GET** `/api/reports`

### 获取特定报告

**GET** `/api/reports/{report_id}`

## 🧪 测试样本

项目包含了丰富的测试样本：

- `test_samples/sample_log.txt` - 包含多种攻击类型的模拟日志
- `test_samples/sample_alert.png` - 模拟告警截图 (需要自行添加)

**测试命令：**
```bash
# 测试文本分析
curl -X POST http://localhost:5000/api/analyze \
  -F "log_text=$(cat test_samples/sample_log.txt)"

# 测试图像分析
curl -X POST http://localhost:5000/api/analyze \
  -F "alert_image=@test_samples/sample_alert.png"
```

## 🔧 配置说明

### 模型配置 (`config.py`)

```python
MODEL_CONFIG = {
    "qwen_vl_model": "Qwen/Qwen-VL-Chat",  # 多模态模型
    "qwen_vl_device": "cuda:0",            # 设备选择
    "generation_config": {
        "max_new_tokens": 1024,
        "temperature": 0.1
    }
}
```

### 安全配置

```python
SECURITY_CONFIG = {
    "threat_categories": [
        "SQL注入攻击", "XSS攻击", "CSRF攻击", 
        "文件上传漏洞", "目录遍历", "命令注入"
    ],
    "input_filters": {
        "max_log_size": 10 * 1024 * 1024,  # 10MB
        "blocked_patterns": [r"<script.*?>.*?</script>"]
    }
}
```

## 📊 项目结构

```
secvizlm/
├── app.py                  # Flask 主应用
├── multimodal_engine.py    # 多模态处理核心
├── security_utils.py       # 安全工具和报告生成
├── config.py               # 配置文件
├── requirements.txt        # 依赖包列表
├── README.md              # 项目文档
├── test_samples/          # 测试样本
│   ├── sample_log.txt     # 模拟安全日志
│   └── sample_alert.png   # 模拟告警截图
├── uploads/               # 上传文件临时目录
├── outputs/               # 分析报告输出目录
└── logs/                  # 系统日志目录
```

## 🎓 毕业设计亮点

### 技术创新点

1. **多模态融合**：首次将文本日志和图像截图结合进行安全分析
2. **大模型应用**：使用 Qwen-VL-Chat 进行智能威胁识别
3. **实时处理**：支持实时上传和分析，适用于 SOC 环境
4. **标准化输出**：基于 OWASP 标准的结构化报告

### 工程实践

1. **模块化设计**：清晰的代码架构，易于扩展和维护
2. **安全防护**：输入过滤、文件验证等安全措施
3. **API 设计**：RESTful API + Swagger 文档
4. **错误处理**：完善的异常处理和日志记录

## 🔮 未来扩展

- [ ] 支持更多日志格式 (Syslog, CEF, LEEF)
- [ ] 集成更多威胁情报源
- [ ] 添加实时流处理能力
- [ ] 支持自定义威胁检测规则
- [ ] 集成 SIEM 系统接口

## 📄 许可证

本项目仅用于学术研究和毕业设计，请勿用于商业用途。

## 👨‍💻 作者

**毕业设计学生** - 计算机科学与技术专业

---

*如有问题或建议，欢迎提交 Issue 或 Pull Request！*
